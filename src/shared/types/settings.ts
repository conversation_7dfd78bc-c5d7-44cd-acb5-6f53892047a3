// 定义主题类型
export type ThemeType = 'system' | 'light' | 'dark';

export type LanguageType = 'zh' | 'en';

export type DisplayScreenType = 'main' | 'mouse';

export type MainWindowStyle = 'complete' | 'simple' | 'left-bar' | 'right-bar';

// 剪贴板历史窗口样式类型
export type ClipboardWindowStyle = 'bottom-bar' | 'center-window';

// 定义配置类型
export interface AppSettings {
  general: {
    launchAtLogin: boolean; // 开机时自动运行
    showTray: boolean; // 显示系统托盘图标
    hotkey: string; // 唤起应用的快捷键
    language: LanguageType; // 界面语言
    displayScreen: DisplayScreenType; // 窗口显示屏幕
    theme: ThemeType; // 主题
    mainWindowStyle: MainWindowStyle; // 窗口样式
    appearance: string; // 外观
    pinned: boolean; // 窗口钉住状态
  },
  clipboard: {
    hotkey: string; // 唤起剪切板历史窗口的快捷键
    maxHistoryCount: number; // 最大历史记录数
    clipboardWindowStyle: ClipboardWindowStyle; // 剪切板历史窗口显示位置
    autoHide: boolean; // 自动隐藏剪切板历史窗口
    checkInterval: number; // 剪切板检查间隔（毫秒）
  },
  ai: {
    model: string; // 模型
    temperature: number; // 温度
    maxTokens: number; // 最大令牌数
  },
  desktopAssistant: {
    floatingBall: 'floating-ball' | 'live2d'; // 悬浮球助手开关
    agentId: string; // 助手角色id
  },
  pet: {
    available: boolean;
    alwaysOnTop: boolean;
    recommend: boolean;
    showTool: boolean;
  }
} 